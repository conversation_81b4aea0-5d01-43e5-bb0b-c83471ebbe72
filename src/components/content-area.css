/* Content Area Component Styles */

/* Main container */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--background-primary);
  min-width: 0;
}

/* Selected files section */
.selected-files-content-area {
  margin-top: auto;
  height: 25vh;
  display: flex;
  flex-direction: column;
}

.selected-files-content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1.5rem;
  border-bottom: 0.0625rem solid var(--border-color);
  background-color: var(--background-primary);
}

.content-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.file-stats {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Prompts buttons */
.prompts-buttons-container {
  display: flex;
  gap: 0.5rem;
}

.system-prompts-button, 
.role-prompts-button, 
.docs-button {
  display: flex;
  align-items: center;
  gap: .25rem;
  padding: 0.375rem 0.75rem;
  background-color: var(--background-secondary);
  border: none;
  border-radius: 0.25rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: background-color 0.2s;
  height: 2rem;
}

.system-prompts-button:hover, 
.role-prompts-button:hover, 
.docs-button:hover {
  background-color: var(--hover-color);
}

.role-prompts-button {
  background-color: rgba(230, 126, 34, 0.1);
}

.docs-button {
  background-color: rgba(41, 128, 185, 0.1);
}

.system-prompts-button:hover {
  background-color: var(--background-tertiary);
}

.selected-prompt-indicator {
  font-weight: 500;
  color: var(--accent-color);
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: .15rem;
}

.selected-prompt-indicator svg {
  position: relative;
  top: 1px;
  color: var(--success-color);
}

/* Clear all button */
.clear-all-button {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.375rem 0.75rem;
  background-color: rgba(239, 68, 68, 0.1);
  border: none;
  border-radius: 0.25rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  height: 2rem;
  font-weight: 500;
}

.clear-all-button:hover {
  background-color: rgba(239, 68, 68, 0.2);
  transform: translateY(-1px);
}

.clear-all-button:active {
  transform: translateY(0);
}

/* User instructions input area */
.user-instructions-input-area {
  padding: 1rem 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 9.375rem;
  position: relative;
}

.instructions-token-count {
  font-size: 0.625rem;
  color: var(--secondary-text-color);
  margin-bottom: 0.25rem;
  text-align: right;
}

.user-instructions-input {
  width: 100%;
  height: 100%;
  padding: 1rem;
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.375rem;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: none;
  transition: all 0.2s ease;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05);
  outline: none;
}

.user-instructions-input:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 0.125rem var(--background-tertiary);
}

.user-instructions-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.6;
}

/* Autocomplete dropdown container */
.autocomplete-container {
  position: relative;
  height: 100%;
}

/* Autocomplete dropdown */
@keyframes autocomplete-appear {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.autocomplete-dropdown {
  position: absolute;
  max-height: 200px;
  overflow-y: auto;
  width: max(200px, 33.33%);
  background: var(--background-elevated);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 5;
  animation: autocomplete-appear 0.15s ease-out;
}

.autocomplete-dropdown::-webkit-scrollbar {
  width: 6px;
}

.autocomplete-dropdown::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
  margin: 4px;
}

.autocomplete-dropdown::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.autocomplete-dropdown::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* Autocomplete dropdown header */
.autocomplete-header {
  padding: 4px 10px;
  font-size: 0.6875rem;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
}

/* Autocomplete items */
.autocomplete-item {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  cursor: pointer;
  background: transparent;
  color: var(--text-primary);
  user-select: none;
  transition: background-color 0.15s ease;
}

.autocomplete-item:hover,
.autocomplete-item.active {
  background: var(--background-selected);
}

.autocomplete-item span {
  font-family: inherit;
  font-size: 0.78125rem;
  letter-spacing: 0.0125rem;
}

/* Token count display */
.token-count-display {
  font-size: 11px;
  color: var(--text-secondary);
  text-align: center;
  width: 100%;
  padding: 0.125rem 0;
  opacity: 0.85;
  letter-spacing: 0.025em;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 1.25rem;
}

/* Sort dropdown in selected files */
.sort-dropdown-selected-files {
  position: relative;
}

/* Editor variants (for modal content areas) */
.content-area.system-prompt-editor,
.content-area.role-prompt-editor,
.content-area.instruction-editor {
  padding: 2rem;
  background-color: var(--background-primary);
  border-radius: 0;
  flex: 1;
  overflow-y: auto;
}

/* Animation moved to src/styles/index.css for shared use */