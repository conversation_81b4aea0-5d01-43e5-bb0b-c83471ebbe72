{"extends": "./tsconfig.base.json", "compilerOptions": {"module": "CommonJS", "target": "ES2020", "moduleResolution": "Node", "outDir": "build", "rootDir": "src", "noEmit": false, "sourceMap": true, "skipLibCheck": true, "esModuleInterop": true, "resolveJsonModule": true, "types": ["node", "electron"], "lib": ["ES2020"]}, "include": ["src/main/**/*.ts", "src/security/**/*.ts", "src/validation/**/*.ts", "src/constants/**/*.ts", "src/shared/**/*.ts", "src/services/token-service.ts", "src/services/token-service-main.ts", "src/types/**/*.d.ts"], "exclude": ["src/**/__tests__/**", "src/**/__mocks__/**"]}